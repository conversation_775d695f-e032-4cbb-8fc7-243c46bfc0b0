import { useState, useEffect, useRef, useCallback } from 'react';

interface UseTypewriterProps {
  text: string;
  speed?: number;
  delay?: number;
  loop?: boolean;
  initialDelay?: number;
}

export const useTypewriter = ({
  text,
  speed = 50,
  delay = 1000,
  loop = true,
  initialDelay = 500
}: UseTypewriterProps) => {
  const [displayText, setDisplayText] = useState('');
  const [showCursor, setShowCursor] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [isActive, setIsActive] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Function to reset the typewriter
  const resetTypewriter = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setDisplayText('');
    setIsDeleting(false);
    setIsPaused(false);
  }, []);

  // Function to pause the typewriter
  const pauseTypewriter = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsActive(false);
  }, []);

  // Function to resume the typewriter
  const resumeTypewriter = useCallback(() => {
    // Add a small delay before starting the animation
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(() => {
      setIsActive(true);
    }, initialDelay);
  }, [initialDelay]);

  useEffect(() => {
    // Start with a delay
    timeoutRef.current = setTimeout(() => {
      setIsActive(true);
    }, initialDelay);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [initialDelay]);

  useEffect(() => {
    if (!isActive) {
      return;
    }

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    const type = () => {
      if (isDeleting) {
        // Deleting text
        if (displayText.length > 0) {
          setDisplayText(prev => prev.slice(0, -1));
          timeoutRef.current = setTimeout(type, speed / 2);
        } else {
          // Finished deleting, pause before restarting
          setIsDeleting(false);
          setIsPaused(true);
          timeoutRef.current = setTimeout(() => {
            setIsPaused(false);
            if (loop) {
              type();
            }
          }, delay);
        }
      } else {
        // Typing text
        if (displayText.length < text.length) {
          setDisplayText(prev => text.slice(0, prev.length + 1));
          timeoutRef.current = setTimeout(type, speed);
        } else {
          // Finished typing, pause before deleting
          setIsPaused(true);
          timeoutRef.current = setTimeout(() => {
            setIsPaused(false);
            if (loop) {
              setIsDeleting(true);
              type();
            }
          }, delay);
        }
      }
    };

    // Start the typing effect
    timeoutRef.current = setTimeout(type, speed);

    // Clean up on unmount
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [text, speed, delay, loop, displayText, isDeleting, isPaused, isActive]);

  // Blinking cursor effect
  useEffect(() => {
    const cursorInterval = setInterval(() => {
      setShowCursor(prev => !prev);
    }, 500);

    return () => clearInterval(cursorInterval);
  }, []);

  return { 
    displayText, 
    showCursor, 
    resetTypewriter, 
    pauseTypewriter, 
    resumeTypewriter 
  };
};